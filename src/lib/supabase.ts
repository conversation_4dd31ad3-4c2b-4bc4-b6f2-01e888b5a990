import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  subscription_tier: 'free' | 'pro' | 'premium';
  subscription_status: 'inactive' | 'active' | 'canceled' | 'past_due';
  stripe_customer_id?: string;
  subscription_end_date?: string;
  usage_count: number;
  monthly_limit: number;
  created_at: string;
  updated_at: string;
}

export interface UserProject {
  id: string;
  user_id: string;
  media_id: string;
  title: string;
  script_content?: string;
  status: 'draft' | 'processing' | 'completed' | 'exported';
  created_at: string;
  updated_at: string;
}