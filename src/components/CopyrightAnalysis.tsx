import React from 'react';
import { Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface CopyrightAnalysisProps {
  analysis: {
    risk_level: 'low' | 'medium' | 'high';
    suggestions: string[];
  };
}

const CopyrightAnalysis: React.FC<CopyrightAnalysisProps> = ({ analysis }) => {
  const getRiskConfig = (level: string) => {
    switch (level) {
      case 'low':
        return {
          icon: CheckCircle,
          color: 'success',
          bgColor: 'bg-success-50',
          borderColor: 'border-success-200',
          textColor: 'text-success-800',
          iconColor: 'text-success-600',
          title: '版权风险较低',
          description: '当前内容使用方式相对安全'
        };
      case 'medium':
        return {
          icon: AlertTriangle,
          color: 'warning',
          bgColor: 'bg-warning-50',
          borderColor: 'border-warning-200',
          textColor: 'text-warning-800',
          iconColor: 'text-warning-600',
          title: '版权风险中等',
          description: '建议采取一些预防措施降低风险'
        };
      case 'high':
        return {
          icon: XCircle,
          color: 'error',
          bgColor: 'bg-error-50',
          borderColor: 'border-error-200',
          textColor: 'text-error-800',
          iconColor: 'text-error-600',
          title: '版权风险较高',
          description: '强烈建议修改使用方式以避免版权问题'
        };
      default:
        return {
          icon: Shield,
          color: 'slate',
          bgColor: 'bg-slate-50',
          borderColor: 'border-slate-200',
          textColor: 'text-slate-800',
          iconColor: 'text-slate-600',
          title: '版权分析中',
          description: '正在分析内容的版权风险...'
        };
    }
  };

  const config = getRiskConfig(analysis.risk_level);
  const IconComponent = config.icon;

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center">
          <Shield className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-slate-900">版权风险评估</h3>
          <p className="text-sm text-slate-600">AI智能分析内容版权风险并提供建议</p>
        </div>
      </div>

      <div className={`p-4 ${config.bgColor} border ${config.borderColor} rounded-xl mb-4`}>
        <div className="flex items-center space-x-3 mb-3">
          <IconComponent className={`w-6 h-6 ${config.iconColor}`} />
          <div>
            <h4 className={`font-semibold ${config.textColor}`}>{config.title}</h4>
            <p className={`text-sm ${config.textColor} opacity-80`}>{config.description}</p>
          </div>
        </div>

        {analysis.suggestions.length > 0 && (
          <div className="space-y-2">
            <h5 className={`text-sm font-medium ${config.textColor} mb-2`}>优化建议:</h5>
            <ul className="space-y-2">
              {analysis.suggestions.map((suggestion, index) => (
                <li key={index} className={`text-sm ${config.textColor} opacity-90 flex items-start space-x-2`}>
                  <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Additional Tips */}
      <div className="p-4 bg-slate-50 rounded-xl">
        <h5 className="text-sm font-semibold text-slate-900 mb-2">版权使用小贴士</h5>
        <ul className="text-sm text-slate-600 space-y-1">
          <li>• 合理使用时长一般不超过原作品的10%-15%</li>
          <li>• 添加个人解说和评论可以增强"转化性使用"的合理性</li>
          <li>• 避免使用高潮或核心情节片段</li>
          <li>• 建议在视频中添加免责声明</li>
        </ul>
      </div>
    </div>
  );
};

export default CopyrightAnalysis;