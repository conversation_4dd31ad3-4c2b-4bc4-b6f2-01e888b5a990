import React, { useState } from 'react';
import { Search, Sparkles, TrendingUp } from 'lucide-react';

interface SearchSectionProps {
  onSearch: (query: string) => void;
  isSearching: boolean;
}

const SearchSection: React.FC<SearchSectionProps> = ({ onSearch, isSearching }) => {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Hero Section */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl shadow-2xl">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 mb-6">
            AI影视解说
            <span className="block bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              创作新体验
            </span>
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            输入电影或电视剧名称，AI为您生成高质量解说文案，
            <br className="hidden sm:block" />
            让创作从"天"缩短至"分钟"
          </p>
        </div>

        {/* Search Form */}
        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 text-slate-400" />
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="搜索电影或电视剧名称..."
              className="w-full pl-16 pr-32 py-5 text-lg border-2 border-slate-200 rounded-2xl focus:border-primary-500 focus:ring-4 focus:ring-primary-100 outline-none transition-all bg-white shadow-lg"
              disabled={isSearching}
            />
            <button
              type="submit"
              disabled={isSearching || !query.trim()}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium shadow-lg hover:shadow-xl"
            >
              {isSearching ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>搜索中</span>
                </div>
              ) : (
                '开始创作'
              )}
            </button>
          </div>
        </form>

        {/* Quick Actions */}
        <div className="flex flex-wrap items-center justify-center gap-4 text-sm">
          <span className="text-slate-500">热门搜索:</span>
          {['流浪地球2', '满江红', '深海', '阿凡达2'].map((term) => (
            <button
              key={term}
              onClick={() => {
                setQuery(term);
                onSearch(term);
              }}
              className="px-4 py-2 bg-slate-100 hover:bg-primary-50 hover:text-primary-600 text-slate-600 rounded-lg transition-all"
            >
              {term}
            </button>
          ))}
        </div>
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow"></div>
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-56 h-56 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>
    </section>
  );
};

export default SearchSection;