import React, { useState } from 'react';
import { X, Check, Sparkles, Zap, Crown } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SubscriptionModal: React.FC<SubscriptionModalProps> = ({ isOpen, onClose }) => {
  const [selectedPlan, setSelectedPlan] = useState<'pro' | 'premium'>('pro');
  const [loading, setLoading] = useState(false);
  const { profile } = useAuth();

  const plans = {
    free: {
      name: '免费版',
      price: '¥0',
      period: '永久',
      icon: Sparkles,
      color: 'from-slate-500 to-slate-600',
      features: [
        '每月3个解说项目',
        '基础AI文案生成',
        '标准导出格式',
        '社区支持'
      ],
      limitations: [
        '功能受限',
        '无优先支持',
        '有使用限制'
      ]
    },
    pro: {
      name: 'Pro版',
      price: '¥99',
      period: '月',
      icon: Zap,
      color: 'from-primary-500 to-primary-600',
      popular: true,
      features: [
        '无限解说项目',
        '高级AI文案生成',
        '多种风格选择',
        '高清视频处理',
        '优先客服支持',
        '批量导出功能',
        '版权风险分析',
        '自定义模板'
      ]
    },
    premium: {
      name: 'Premium版',
      price: '¥199',
      period: '月',
      icon: Crown,
      color: 'from-warning-500 to-error-500',
      features: [
        'Pro版全部功能',
        'AI语音克隆',
        '专属BGM库',
        '4K视频处理',
        '白标定制',
        '专属客户经理',
        'API接口访问',
        '团队协作功能',
        '数据分析报告'
      ]
    }
  };

  const handleSubscribe = async (planType: 'pro' | 'premium') => {
    setLoading(true);
    
    try {
      // Here you would integrate with Stripe
      // For now, we'll simulate the process
      console.log(`Subscribing to ${planType} plan`);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, you would:
      // 1. Create a Stripe checkout session
      // 2. Redirect to Stripe checkout
      // 3. Handle the webhook to update user subscription
      
      alert('支付功能正在开发中，敬请期待！');
    } catch (error) {
      console.error('Subscription error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-slate-900 mb-2">选择您的订阅计划</h2>
            <p className="text-slate-600">解锁更多AI创作功能，提升您的内容制作效率</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {Object.entries(plans).map(([key, plan]) => {
            const IconComponent = plan.icon;
            const isCurrentPlan = profile?.subscription_tier === key;
            const isSelected = selectedPlan === key;
            
            return (
              <div
                key={key}
                className={`relative p-6 border-2 rounded-2xl transition-all cursor-pointer ${
                  plan.popular 
                    ? 'border-primary-500 bg-primary-50' 
                    : isSelected 
                      ? 'border-primary-300 bg-primary-25' 
                      : 'border-slate-200 hover:border-slate-300'
                } ${key !== 'free' ? 'hover:shadow-lg' : ''}`}
                onClick={() => key !== 'free' && setSelectedPlan(key as 'pro' | 'premium')}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      最受欢迎
                    </span>
                  </div>
                )}

                {isCurrentPlan && (
                  <div className="absolute -top-3 right-4">
                    <span className="bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      当前计划
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-2">{plan.name}</h3>
                  <div className="flex items-baseline justify-center space-x-1">
                    <span className="text-3xl font-bold text-slate-900">{plan.price}</span>
                    <span className="text-slate-600">/{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3">
                      <Check className="w-5 h-5 text-success-600 flex-shrink-0" />
                      <span className="text-sm text-slate-700">{feature}</span>
                    </li>
                  ))}
                  {plan.limitations?.map((limitation, index) => (
                    <li key={index} className="flex items-center space-x-3 opacity-60">
                      <X className="w-5 h-5 text-slate-400 flex-shrink-0" />
                      <span className="text-sm text-slate-500">{limitation}</span>
                    </li>
                  ))}
                </ul>

                {key === 'free' ? (
                  <button
                    disabled
                    className="w-full py-3 bg-slate-100 text-slate-500 rounded-xl font-medium cursor-not-allowed"
                  >
                    {isCurrentPlan ? '当前计划' : '免费使用'}
                  </button>
                ) : (
                  <button
                    onClick={() => handleSubscribe(key as 'pro' | 'premium')}
                    disabled={loading || isCurrentPlan}
                    className={`w-full py-3 rounded-xl font-medium transition-all ${
                      isCurrentPlan
                        ? 'bg-success-100 text-success-700 cursor-not-allowed'
                        : plan.popular
                          ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 shadow-lg hover:shadow-xl'
                          : 'bg-slate-900 text-white hover:bg-slate-800'
                    }`}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>处理中...</span>
                      </div>
                    ) : isCurrentPlan ? (
                      '当前计划'
                    ) : (
                      `升级到 ${plan.name}`
                    )}
                  </button>
                )}
              </div>
            );
          })}
        </div>

        <div className="bg-slate-50 rounded-xl p-6">
          <h4 className="font-semibold text-slate-900 mb-3">常见问题</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-slate-800 mb-1">可以随时取消订阅吗？</p>
              <p className="text-slate-600">是的，您可以随时在账户设置中取消订阅，取消后仍可使用至当前计费周期结束。</p>
            </div>
            <div>
              <p className="font-medium text-slate-800 mb-1">支持哪些支付方式？</p>
              <p className="text-slate-600">支持微信支付、支付宝、银行卡等多种支付方式，安全便捷。</p>
            </div>
            <div>
              <p className="font-medium text-slate-800 mb-1">有免费试用吗？</p>
              <p className="text-slate-600">新用户注册即可免费体验，每月3个项目额度，无需绑定支付方式。</p>
            </div>
            <div>
              <p className="font-medium text-slate-800 mb-1">企业用户有优惠吗？</p>
              <p className="text-slate-600">我们为企业用户提供定制化方案和批量优惠，请联系客服咨询。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionModal;