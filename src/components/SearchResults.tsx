import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, Calendar, PlayCircle } from 'lucide-react';

interface SearchResult {
  media_id: string;
  title: string;
  original_title: string;
  year: number;
  poster_url: string;
  genres: string[];
}

interface SearchResultsProps {
  results: SearchResult[];
  query: string;
}

const SearchResults: React.FC<SearchResultsProps> = ({ results, query }) => {
  const navigate = useNavigate();

  const handleItemClick = (mediaId: string) => {
    navigate(`/details/${mediaId}`);
  };

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-slate-900 mb-2">
            搜索结果
          </h2>
          <p className="text-slate-600">
            为 <span className="font-semibold text-primary-600">"{query}"</span> 找到 {results.length} 个结果
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {results.map((item) => (
            <div
              key={item.media_id}
              onClick={() => handleItemClick(item.media_id)}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer border border-slate-100 hover:border-primary-200 animate-slide-up"
            >
              <div className="relative h-80 overflow-hidden">
                <img
                  src={item.poster_url}
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="absolute bottom-4 left-4 right-4">
                    <button className="flex items-center justify-center w-full py-3 bg-primary-500/90 backdrop-blur-sm text-white rounded-xl hover:bg-primary-600/90 transition-all font-medium">
                      <PlayCircle className="w-5 h-5 mr-2" />
                      生成解说文案
                    </button>
                  </div>
                </div>
                
                {/* Year Badge */}
                <div className="absolute top-4 right-4">
                  <div className="flex items-center space-x-1 px-2 py-1 bg-black/60 backdrop-blur-sm text-white rounded-lg text-sm">
                    <Calendar className="w-3 h-3" />
                    <span>{item.year}</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="font-bold text-slate-900 mb-1 group-hover:text-primary-600 transition-colors line-clamp-1">
                  {item.title}
                </h3>
                
                {item.original_title !== item.title && (
                  <p className="text-sm text-slate-500 mb-3 line-clamp-1">
                    {item.original_title}
                  </p>
                )}

                <div className="flex flex-wrap gap-2">
                  {item.genres.slice(0, 3).map((genre) => (
                    <span
                      key={genre}
                      className="px-2 py-1 bg-slate-100 text-slate-600 rounded-lg text-xs font-medium"
                    >
                      {genre}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SearchResults;