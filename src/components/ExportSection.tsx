import React, { useState } from 'react';
import { Download, Package, Check, ExternalLink } from 'lucide-react';

interface ExportSectionProps {
  scriptId: string;
  movieTitle: string;
}

const ExportSection: React.FC<ExportSectionProps> = ({ scriptId, movieTitle }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportComplete, setExportComplete] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    // Simulate export process
    const stages = [
      { progress: 20, message: '生成AI配音...' },
      { progress: 40, message: '智能选择BGM...' },
      { progress: 60, message: '构建剪映草稿...' },
      { progress: 85, message: '打包导出文件...' },
      { progress: 100, message: '完成!' }
    ];

    for (const stage of stages) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      setExportProgress(stage.progress);
    }

    setIsExporting(false);
    setExportComplete(true);
    setDownloadUrl(`https://storage.example.com/exports/${scriptId}.zip`);
  };

  const handleDownload = () => {
    if (downloadUrl) {
      // Simulate download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${movieTitle}-解说文案.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-accent-600 rounded-xl flex items-center justify-center">
          <Package className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-slate-900">导出剪映草稿</h3>
          <p className="text-sm text-slate-600">一键生成包含配音、BGM、字幕的完整工程文件</p>
        </div>
      </div>

      {!isExporting && !exportComplete && (
        <div className="space-y-4">
          <div className="p-4 bg-gradient-to-r from-accent-50 to-success-50 border border-accent-200 rounded-xl">
            <h4 className="font-semibold text-slate-900 mb-2">导出内容包含:</h4>
            <ul className="text-sm text-slate-700 space-y-1">
              <li className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-accent-600" />
                <span>AI生成的高质量配音文件</span>
              </li>
              <li className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-accent-600" />
                <span>智能匹配的背景音乐</span>
              </li>
              <li className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-accent-600" />
                <span>完整的字幕文件</span>
              </li>
              <li className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-accent-600" />
                <span>剪映可直接导入的工程文件</span>
              </li>
            </ul>
          </div>

          <button
            onClick={handleExport}
            className="w-full py-4 bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-xl hover:from-accent-600 hover:to-accent-700 transition-all font-medium shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
          >
            <Download className="w-5 h-5" />
            <span>生成剪映草稿</span>
          </button>
        </div>
      )}

      {isExporting && (
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="font-medium text-blue-800">正在生成草稿文件...</p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-blue-700">
                  {exportProgress < 20 ? '生成AI配音...' :
                   exportProgress < 40 ? '智能选择BGM...' :
                   exportProgress < 60 ? '构建剪映草稿...' :
                   exportProgress < 85 ? '打包导出文件...' : '完成!'}
                </span>
                <span className="font-medium text-blue-900">{exportProgress}%</span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {exportComplete && (
        <div className="space-y-4">
          <div className="p-4 bg-success-50 border border-success-200 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <Check className="w-6 h-6 text-success-600" />
              <div>
                <p className="font-medium text-success-800">草稿生成完成!</p>
                <p className="text-sm text-success-700">所有文件已打包完成，可以下载使用了</p>
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleDownload}
              className="flex-1 py-3 bg-gradient-to-r from-success-500 to-success-600 text-white rounded-xl hover:from-success-600 hover:to-success-700 transition-all font-medium flex items-center justify-center space-x-2"
            >
              <Download className="w-5 h-5" />
              <span>下载草稿文件</span>
            </button>
            
            <button className="px-6 py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-all flex items-center space-x-2">
              <ExternalLink className="w-4 h-4" />
              <span>教程</span>
            </button>
          </div>

          <div className="p-4 bg-slate-50 rounded-xl">
            <h5 className="text-sm font-semibold text-slate-900 mb-2">使用说明</h5>
            <ul className="text-sm text-slate-600 space-y-1">
              <li>1. 下载并解压文件到本地</li>
              <li>2. 打开剪映专业版，选择"导入草稿"</li>
              <li>3. 选择解压后的草稿文件夹</li>
              <li>4. 进行最终的精修和调整</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportSection;