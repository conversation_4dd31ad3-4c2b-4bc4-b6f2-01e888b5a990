import React, { useState, useEffect } from 'react';
import { Edit3, Clock, Download, Copy, Check } from 'lucide-react';

interface ScriptEditorProps {
  scriptData: any;
  movieTitle: string;
}

const ScriptEditor: React.FC<ScriptEditorProps> = ({ scriptData, movieTitle }) => {
  const [content, setContent] = useState('');
  const [copied, setCopied] = useState(false);
  const [hasTimestamps, setHasTimestamps] = useState(false);

  useEffect(() => {
    if (scriptData) {
      setContent(scriptData.content_html);
      // Simulate timestamp matching completion
      setTimeout(() => {
        setHasTimestamps(true);
        // Add timestamps to content
        const contentWithTimestamps = scriptData.content_html
          .replace(/<p>/g, '<p><span class="timestamp">[00:00:15 - 00:00:32]</span>')
          .replace(/(<p><span class="timestamp">\[00:00:15 - 00:00:32\]<\/span>)/g, 
            (match, p1, offset, string) => {
              const paragraphIndex = (string.substring(0, offset).match(/<p>/g) || []).length;
              const timestamps = [
                '[00:00:15 - 00:00:32]',
                '[00:00:33 - 00:00:58]',
                '[00:01:02 - 00:01:28]',
                '[00:01:30 - 00:02:15]'
              ];
              return `<p><span class="timestamp">${timestamps[paragraphIndex] || '[00:00:00 - 00:00:15]'}</span>`;
            });
        setContent(contentWithTimestamps);
      }, 5000);
    }
  }, [scriptData]);

  const handleCopy = async () => {
    try {
      // Remove HTML tags for plain text copy
      const plainText = content.replace(/<[^>]*>/g, '').replace(/\[.*?\]/g, '');
      await navigator.clipboard.writeText(plainText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  if (!scriptData) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Edit3 className="w-8 h-8 text-slate-400" />
          </div>
          <h3 className="text-xl font-semibold text-slate-900 mb-2">AI解说文案</h3>
          <p className="text-slate-600 max-w-md mx-auto">
            选择左侧影片信息，点击"生成解说文案"开始创作您的专属解说内容
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-slate-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
              <Edit3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900">AI解说文案</h3>
              <p className="text-sm text-slate-600">{movieTitle}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasTimestamps && (
              <div className="flex items-center space-x-1 px-3 py-1 bg-success-50 text-success-600 rounded-lg text-sm">
                <Clock className="w-4 h-4" />
                <span>已匹配时间轴</span>
              </div>
            )}
            
            <button
              onClick={handleCopy}
              className="p-2 text-slate-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-all"
              title="复制文案"
            >
              {copied ? <Check className="w-5 h-5" /> : <Copy className="w-5 h-5" />}
            </button>
            
            <button className="p-2 text-slate-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-all">
              <Download className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div 
          className="prose prose-slate max-w-none"
          style={{
            minHeight: '400px',
            lineHeight: '1.8'
          }}
        >
          <style>
            {`
              .timestamp {
                display: inline-block;
                background: linear-gradient(135deg, #8b5cf6, #3b82f6);
                color: white;
                padding: 2px 8px;
                border-radius: 6px;
                font-size: 0.75rem;
                font-weight: 500;
                margin-right: 12px;
                margin-bottom: 8px;
                font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
              }
              .prose p {
                margin-bottom: 1.5rem;
                color: #334155;
                font-size: 1rem;
              }
            `}
          </style>
          <div dangerouslySetInnerHTML={{ __html: content }} />
        </div>

        {scriptData.status === 'matching' && (
          <div className="mt-6 p-4 bg-warning-50 border border-warning-200 rounded-xl">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 border-2 border-warning-500 border-t-transparent rounded-full animate-spin"></div>
              <div>
                <p className="font-medium text-warning-800">正在匹配时间轴...</p>
                <p className="text-sm text-warning-700">AI正在分析视频内容并自动匹配解说时间点</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ScriptEditor;