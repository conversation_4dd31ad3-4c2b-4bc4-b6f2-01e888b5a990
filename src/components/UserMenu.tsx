import React, { useState } from 'react';
import { User, <PERSON><PERSON><PERSON>, CreditCard, LogOut, ChevronDown } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface UserMenuProps {
  onOpenSubscription: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ onOpenSubscription }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, profile, signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    setIsOpen(false);
  };

  const getSubscriptionBadge = () => {
    if (!profile) return null;
    
    const badges = {
      free: { text: '免费版', color: 'bg-slate-100 text-slate-600' },
      pro: { text: 'Pro', color: 'bg-primary-100 text-primary-600' },
      premium: { text: 'Premium', color: 'bg-warning-100 text-warning-600' }
    };

    const badge = badges[profile.subscription_tier];
    return (
      <span className={`px-2 py-1 rounded text-xs font-medium ${badge.color}`}>
        {badge.text}
      </span>
    );
  };

  const getUsageInfo = () => {
    if (!profile) return null;
    
    if (profile.subscription_tier === 'free') {
      return (
        <div className="px-4 py-3 border-b border-slate-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-slate-600">本月使用量</span>
            <span className="text-sm font-medium text-slate-900">
              {profile.usage_count}/{profile.monthly_limit}
            </span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div 
              className="bg-primary-500 h-2 rounded-full transition-all"
              style={{ width: `${(profile.usage_count / profile.monthly_limit) * 100}%` }}
            ></div>
          </div>
          {profile.usage_count >= profile.monthly_limit && (
            <p className="text-xs text-error-600 mt-2">
              已达到免费额度上限，升级Pro版解锁无限使用
            </p>
          )}
        </div>
      );
    }

    return null;
  };

  if (!user || !profile) return null;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 p-2 rounded-xl hover:bg-slate-50 transition-all"
      >
        <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
          <User className="w-5 h-5 text-white" />
        </div>
        <div className="hidden md:block text-left">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-slate-900">
              {profile.full_name || '用户'}
            </p>
            {getSubscriptionBadge()}
          </div>
          <p className="text-xs text-slate-500">{user.email}</p>
        </div>
        <ChevronDown className="w-4 h-4 text-slate-400" />
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          ></div>
          <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-2xl shadow-2xl border border-slate-200 z-20">
            {/* User Info */}
            <div className="px-4 py-4 border-b border-slate-200">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="font-medium text-slate-900">
                      {profile.full_name || '用户'}
                    </p>
                    {getSubscriptionBadge()}
                  </div>
                  <p className="text-sm text-slate-500">{user.email}</p>
                </div>
              </div>
            </div>

            {/* Usage Info */}
            {getUsageInfo()}

            {/* Menu Items */}
            <div className="py-2">
              <button className="w-full px-4 py-3 text-left hover:bg-slate-50 transition-all flex items-center space-x-3">
                <Settings className="w-5 h-5 text-slate-400" />
                <span className="text-sm text-slate-700">账户设置</span>
              </button>
              
              <button 
                onClick={() => {
                  onOpenSubscription();
                  setIsOpen(false);
                }}
                className="w-full px-4 py-3 text-left hover:bg-slate-50 transition-all flex items-center space-x-3"
              >
                <CreditCard className="w-5 h-5 text-slate-400" />
                <span className="text-sm text-slate-700">订阅管理</span>
              </button>
            </div>

            <div className="border-t border-slate-200 py-2">
              <button 
                onClick={handleSignOut}
                className="w-full px-4 py-3 text-left hover:bg-slate-50 transition-all flex items-center space-x-3"
              >
                <LogOut className="w-5 h-5 text-slate-400" />
                <span className="text-sm text-slate-700">退出登录</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserMenu;