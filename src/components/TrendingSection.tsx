import React from 'react';
import { TrendingUp, Clock, Star, Play } from 'lucide-react';

const TrendingSection: React.FC = () => {
  const trendingData = [
    {
      rank: 1,
      title: '流浪地球2',
      heat: '982.1万',
      description: '科幻史诗巨制，视效震撼人心',
      poster: 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17',
      genre: '科幻',
      rating: 8.3
    },
    {
      rank: 2,
      title: '满江红',
      heat: '756.8万',
      description: '张艺谋执导悬疑喜剧',
      poster: 'https://images.unsplash.com/photo-1489599316408-8c4b8d4ff2a4',
      genre: '喜剧',
      rating: 7.9
    },
    {
      rank: 3,
      title: '深海',
      heat: '623.4万',
      description: '国产动画电影新突破',
      poster: 'https://images.unsplash.com/photo-1544552866-d3ed42536cfd',
      genre: '动画',
      rating: 7.2
    },
    {
      rank: 4,
      title: '阿凡达：水之道',
      heat: '589.2万',
      description: '卡梅隆科幻续作',
      poster: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43',
      genre: '科幻',
      rating: 8.1
    }
  ];

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center space-x-4">
            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-warning-500 to-error-500 rounded-xl shadow-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-slate-900">热点选题</h2>
              <p className="text-slate-600">当前网络热门影视内容，助您把握创作风向</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-slate-500">
            <Clock className="w-4 h-4" />
            <span>实时更新</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {trendingData.map((item) => (
            <div
              key={item.rank}
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer border border-slate-100 hover:border-primary-200"
            >
              {/* Rank Badge */}
              <div className="absolute top-4 left-4 z-10">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-white font-bold text-sm shadow-lg ${
                  item.rank === 1 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                  item.rank === 2 ? 'bg-gradient-to-r from-gray-400 to-gray-500' :
                  item.rank === 3 ? 'bg-gradient-to-r from-amber-600 to-amber-700' :
                  'bg-gradient-to-r from-slate-400 to-slate-500'
                }`}>
                  {item.rank}
                </div>
              </div>

              {/* Poster */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={item.poster}
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="absolute bottom-4 left-4 right-4">
                    <button className="flex items-center justify-center w-full py-2 bg-white/20 backdrop-blur-sm text-white rounded-lg hover:bg-white/30 transition-all">
                      <Play className="w-4 h-4 mr-2" />
                      开始创作
                    </button>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="px-2 py-1 bg-primary-50 text-primary-600 rounded text-xs font-medium">
                    {item.genre}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium text-slate-700">{item.rating}</span>
                  </div>
                </div>

                <h3 className="font-bold text-slate-900 mb-2 group-hover:text-primary-600 transition-colors">
                  {item.title}
                </h3>
                
                <p className="text-sm text-slate-600 mb-4 line-clamp-2">
                  {item.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-error-500" />
                    <span className="text-sm font-medium text-error-600">{item.heat}</span>
                  </div>
                  <span className="text-xs text-slate-400">热度指数</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View More */}
        <div className="text-center mt-12">
          <button className="px-8 py-3 border-2 border-slate-200 text-slate-600 rounded-xl hover:border-primary-500 hover:text-primary-600 transition-all font-medium">
            查看更多热门内容
          </button>
        </div>
      </div>
    </section>
  );
};

export default TrendingSection;