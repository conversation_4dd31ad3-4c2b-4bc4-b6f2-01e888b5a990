import React, { useState } from 'react';
import { Upload, Video, Check, AlertCircle } from 'lucide-react';

interface VideoUploadProps {
  onVideoUploaded: (videoUrl: string) => void;
  scriptId: string;
}

const VideoUpload: React.FC<VideoUploadProps> = ({ onVideoUploaded, scriptId }) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadComplete, setUploadComplete] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      handleUpload(file);
    }
  };

  const handleUpload = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setIsUploading(false);
          setUploadComplete(true);
          // Notify parent component
          onVideoUploaded(`https://storage.example.com/videos/${file.name}`);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center">
          <Video className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-slate-900">视频素材上传</h3>
          <p className="text-sm text-slate-600">上传您的视频文件以匹配解说时间轴</p>
        </div>
      </div>

      {!uploadedFile ? (
        <div className="border-2 border-dashed border-slate-300 rounded-xl p-8 text-center hover:border-primary-400 hover:bg-primary-50/50 transition-all">
          <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Upload className="w-8 h-8 text-slate-400" />
          </div>
          <h4 className="text-lg font-semibold text-slate-900 mb-2">上传视频文件</h4>
          <p className="text-slate-600 mb-4">
            支持 MP4, MOV, AVI 格式，最大支持 2GB
          </p>
          <label className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white rounded-xl hover:from-secondary-600 hover:to-secondary-700 cursor-pointer transition-all font-medium">
            <Upload className="w-5 h-5 mr-2" />
            选择文件
            <input
              type="file"
              accept="video/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </label>
        </div>
      ) : (
        <div className="space-y-4">
          {/* File Info */}
          <div className="flex items-center space-x-4 p-4 bg-slate-50 rounded-xl">
            <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
              <Video className="w-6 h-6 text-secondary-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-slate-900">{uploadedFile.name}</h4>
              <p className="text-sm text-slate-600">{formatFileSize(uploadedFile.size)}</p>
            </div>
            {uploadComplete && (
              <div className="w-8 h-8 bg-success-100 rounded-full flex items-center justify-center">
                <Check className="w-5 h-5 text-success-600" />
              </div>
            )}
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">上传进度</span>
                <span className="font-medium text-slate-900">{Math.round(uploadProgress)}%</span>
              </div>
              <div className="w-full bg-slate-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-secondary-500 to-secondary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Upload Complete */}
          {uploadComplete && (
            <div className="p-4 bg-success-50 border border-success-200 rounded-xl">
              <div className="flex items-center space-x-3">
                <Check className="w-6 h-6 text-success-600" />
                <div>
                  <p className="font-medium text-success-800">上传完成</p>
                  <p className="text-sm text-success-700">AI正在分析视频内容并匹配时间轴，请稍候...</p>
                </div>
              </div>
            </div>
          )}

          {/* Tips */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800 mb-1">使用建议</p>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 建议上传清晰度较高的视频文件以获得更好的匹配效果</li>
                  <li>• 时间轴匹配完成后，您可以手动调整时间点</li>
                  <li>• 支持多段视频片段，系统会自动识别最佳匹配点</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUpload;