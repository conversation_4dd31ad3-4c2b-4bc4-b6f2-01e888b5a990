import React, { useState } from 'react';
import SearchSection from '../components/SearchSection';
import TrendingSection from '../components/TrendingSection';
import SearchResults from '../components/SearchResults';

const HomePage: React.FC = () => {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setIsSearching(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockResults = [
        {
          media_id: 'tmdb-12345',
          title: '流浪地球2',
          original_title: 'The Wandering Earth II',
          year: 2023,
          poster_url: 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17',
          genres: ['科幻', '冒险', '灾难']
        },
        {
          media_id: 'tmdb-12346',
          title: '满江红',
          original_title: 'Full River Red',
          year: 2023,
          poster_url: 'https://images.unsplash.com/photo-1489599316408-8c4b8d4ff2a4',
          genres: ['喜剧', '悬疑', '古装']
        },
        {
          media_id: 'tmdb-12347',
          title: '深海',
          original_title: 'Deep Sea',
          year: 2023,
          poster_url: 'https://images.unsplash.com/photo-1544552866-d3ed42536cfd',
          genres: ['动画', '奇幻', '冒险']
        }
      ];
      setSearchResults(mockResults);
      setIsSearching(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen">
      <SearchSection onSearch={handleSearch} isSearching={isSearching} />
      
      {searchResults.length > 0 ? (
        <SearchResults results={searchResults} query={searchQuery} />
      ) : (
        <TrendingSection />
      )}
    </div>
  );
};

export default HomePage;