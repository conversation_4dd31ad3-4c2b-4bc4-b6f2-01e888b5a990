# 认证功能测试指南

## 测试步骤

### 1. 用户注册测试
1. 打开应用 http://localhost:5173
2. 点击右上角的"注册"按钮
3. 填写注册表单：
   - 姓名：测试用户
   - 邮箱：<EMAIL>
   - 密码：123456
4. 点击"创建账户"按钮
5. 验证：
   - 应该显示"注册成功！欢迎使用 NarratoAI"
   - 模态框应该自动关闭
   - 右上角应该显示用户菜单而不是登录按钮

### 2. 用户登录测试
1. 如果已登录，先点击用户菜单中的"退出登录"
2. 点击右上角的"登录"按钮
3. 填写登录表单：
   - 邮箱：<EMAIL>
   - 密码：123456
4. 点击"登录"按钮
5. 验证：
   - 应该显示"登录成功！"
   - 模态框应该自动关闭
   - 右上角应该显示用户菜单

### 3. 表单验证测试
1. 打开注册/登录模态框
2. 测试以下场景：
   - 空邮箱：应该显示验证错误
   - 无效邮箱格式：应该显示"请输入有效的邮箱地址"
   - 密码少于6位：应该显示"密码至少需要6位字符"
   - 注册时空姓名：应该显示验证错误
   - 提交按钮应该在表单无效时被禁用

### 4. 用户资料测试
1. 成功登录后，点击用户菜单
2. 验证：
   - 应该显示用户姓名和邮箱
   - 应该显示"免费版"标签
   - 应该显示使用量信息（0/3）

### 5. 错误处理测试
1. 尝试使用错误的邮箱/密码登录
2. 验证：
   - 应该显示友好的错误消息
   - 不应该显示技术性错误信息

## 预期结果

- ✅ 用户可以成功注册新账户
- ✅ 用户可以成功登录已有账户
- ✅ 表单验证正常工作
- ✅ 错误消息友好且准确
- ✅ 用户资料正确显示
- ✅ 认证状态在页面刷新后保持
- ✅ 用户可以成功登出

## 数据库验证

可以在 Supabase 控制台中检查：
1. `auth.users` 表中应该有新注册的用户
2. `user_profiles` 表中应该有对应的用户资料记录
3. 用户资料应该包含正确的默认值（免费版、3次使用限制等）
