/*
  # Create user profiles and subscription system

  1. New Tables
    - `user_profiles`
      - `id` (uuid, references auth.users)
      - `email` (text)
      - `full_name` (text)
      - `avatar_url` (text, optional)
      - `subscription_tier` (text, default 'free')
      - `subscription_status` (text, default 'inactive')
      - `stripe_customer_id` (text, optional)
      - `subscription_end_date` (timestamptz, optional)
      - `usage_count` (integer, default 0)
      - `monthly_limit` (integer, default 3)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `user_projects`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references user_profiles)
      - `media_id` (text)
      - `title` (text)
      - `script_content` (text)
      - `status` (text, default 'draft')
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage their own data
    - Add policies for reading user profiles (limited fields)